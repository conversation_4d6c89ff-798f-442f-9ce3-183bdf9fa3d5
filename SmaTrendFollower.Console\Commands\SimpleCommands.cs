using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Services;
using SmaTrendFollower.Data;
using SmaTrendFollower.Configuration;
using DotNetEnv;

namespace SmaTrendFollower.Commands;

/// <summary>
/// Simple command handlers for Phase 3 & 4 functionality demonstration
/// </summary>
public static class SimpleCommands
{
    /// <summary>
    /// Handles health status command
    /// </summary>
    public static async Task HandleHealthAsync(IServiceProvider services)
    {
        try
        {
            var healthService = services.GetRequiredService<ISystemHealthService>();
            var status = healthService.GetCurrentStatus();
            var report = await healthService.GetHealthReportAsync();

            System.Console.WriteLine($"🏥 System Health Status: {status}");
            System.Console.WriteLine($"📅 Generated: {report.GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC");
            System.Console.WriteLine();

            System.Console.WriteLine("📊 Component Health:");
            foreach (var check in report.Checks.Values)
            {
                var icon = check.Status switch
                {
                    HealthCheckStatus.Healthy => "✅",
                    HealthCheckStatus.Degraded => "⚠️",
                    HealthCheckStatus.Unhealthy => "❌",
                    _ => "❓"
                };

                System.Console.WriteLine($"  {icon} {check.Name}: {check.Status}");
                if (!string.IsNullOrEmpty(check.Details))
                {
                    System.Console.WriteLine($"     {check.Details}");
                }
            }

            if (report.RecentEvents.Any())
            {
                System.Console.WriteLine();
                System.Console.WriteLine("📋 Recent Events:");
                foreach (var evt in report.RecentEvents.TakeLast(3))
                {
                    var severityIcon = evt.Severity switch
                    {
                        HealthEventSeverity.Critical => "🔴",
                        HealthEventSeverity.High => "🟠",
                        HealthEventSeverity.Medium => "🟡",
                        HealthEventSeverity.Low => "🟢",
                        _ => "⚪"
                    };

                    System.Console.WriteLine($"  {severityIcon} [{evt.Component}] {evt.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error checking health: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles metrics display command
    /// </summary>
    public static async Task HandleMetricsAsync(IServiceProvider services)
    {
        try
        {
            var metricsService = services.GetRequiredService<ITradingMetricsService>();
            var stats = await metricsService.GetTradingStatisticsAsync();
            var kpis = metricsService.GetKPIs();

            System.Console.WriteLine("📈 Trading Statistics:");
            System.Console.WriteLine($"  📊 Total Trades: {stats.TotalTrades}");
            System.Console.WriteLine($"  💰 Profitable Trades: {stats.ProfitableTrades}");
            System.Console.WriteLine($"  🎯 Win Rate: {stats.WinRate:P2}");
            System.Console.WriteLine($"  💵 Total P&L: ${stats.TotalPnL:F2}");
            System.Console.WriteLine($"  📊 Sharpe Ratio: {stats.SharpeRatio:F2}");
            System.Console.WriteLine($"  📡 Total Signals: {stats.TotalSignals}");
            System.Console.WriteLine($"  ✅ Executed Signals: {stats.ExecutedSignals}");
            System.Console.WriteLine($"  🎯 Signal Execution Rate: {stats.SignalExecutionRate:P2}");
            System.Console.WriteLine();

            System.Console.WriteLine("🔑 Key Performance Indicators:");
            foreach (var kpi in kpis.Take(5))
            {
                System.Console.WriteLine($"  📊 {kpi.Key}: {kpi.Value:F2}");
            }

            System.Console.WriteLine($"📅 Generated: {stats.GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error retrieving metrics: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles live market monitoring command
    /// </summary>
    public static Task HandleLiveAsync(IServiceProvider services)
    {
        try
        {
            var marketMonitor = services.GetRequiredService<IRealTimeMarketMonitor>();
            var signalIntelligence = services.GetRequiredService<ILiveSignalIntelligence>();

            var snapshots = marketMonitor.GetAllMarketSnapshots();
            var signals = signalIntelligence.GetLiveSignals();
            var alerts = marketMonitor.GetRecentAlerts(5);

            System.Console.WriteLine($"📡 Live Market Data ({snapshots.Count} symbols monitored):");
            foreach (var snapshot in snapshots.Values.Take(5))
            {
                var trendIcon = snapshot.Trend switch
                {
                    MarketTrend.Bullish => "📈",
                    MarketTrend.Bearish => "📉",
                    MarketTrend.Sideways => "➡️",
                    _ => "❓"
                };

                System.Console.WriteLine($"  {trendIcon} {snapshot.Symbol}: ${snapshot.CurrentPrice:F2} " +
                                        $"({snapshot.PriceChange:P2}) Vol: {snapshot.Volatility:F4}");
            }

            System.Console.WriteLine();
            System.Console.WriteLine($"🧠 Live Signals ({signals.Count} active):");
            foreach (var signal in signals.Take(3))
            {
                System.Console.WriteLine($"  📊 {signal.Symbol}: ${signal.Price:F2} " +
                                        $"Confidence: {signal.Confidence:F2} Score: {signal.IntelligenceScore:F2}");
                System.Console.WriteLine($"     💡 {signal.Reasoning}");
            }

            if (alerts.Any())
            {
                System.Console.WriteLine();
                System.Console.WriteLine($"🚨 Recent Alerts ({alerts.Count}):");
                foreach (var alert in alerts.Take(3))
                {
                    var severityIcon = alert.Severity switch
                    {
                        AlertSeverity.Critical => "🔴",
                        AlertSeverity.High => "🟠",
                        AlertSeverity.Medium => "🟡",
                        AlertSeverity.Low => "🟢",
                        _ => "⚪"
                    };

                    System.Console.WriteLine($"  {severityIcon} [{alert.Symbol}] {alert.Description}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error retrieving live data: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Handles system status command
    /// </summary>
    public static Task HandleSystemAsync(IServiceProvider services)
    {
        try
        {
            var version = typeof(Program).Assembly.GetName().Version?.ToString() ?? "Unknown";
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var startTime = process.StartTime;
            var uptime = DateTime.Now - startTime;
            var memoryMB = process.WorkingSet64 / 1024 / 1024;

            System.Console.WriteLine("🖥️  System Status:");
            System.Console.WriteLine($"  📦 Version: {version}");
            System.Console.WriteLine($"  🌍 Environment: {environment}");
            System.Console.WriteLine($"  🕐 Started: {startTime:yyyy-MM-dd HH:mm:ss}");
            System.Console.WriteLine($"  ⏱️  Uptime: {uptime.Days}d {uptime.Hours}h {uptime.Minutes}m");
            System.Console.WriteLine($"  🆔 Process ID: {Environment.ProcessId}");
            System.Console.WriteLine($"  💻 Machine: {Environment.MachineName}");
            System.Console.WriteLine($"  🖥️  OS: {Environment.OSVersion}");
            System.Console.WriteLine($"  ⚙️  .NET: {Environment.Version}");
            System.Console.WriteLine($"  🧠 Memory: {memoryMB}MB");
            System.Console.WriteLine($"  🧵 Threads: {process.Threads.Count}");

            // Try to get additional metrics
            try
            {
                var metricsService = services.GetRequiredService<ITradingMetricsService>();
                var systemMetrics = metricsService.GetRecentSystemMetrics(5);
                
                if (systemMetrics.Any())
                {
                    System.Console.WriteLine();
                    System.Console.WriteLine("📊 Recent System Metrics:");
                    foreach (var metric in systemMetrics.TakeLast(3))
                    {
                        System.Console.WriteLine($"  📈 {metric.MetricName}: {metric.Value:F2} {metric.Unit}");
                    }
                }
            }
            catch
            {
                // Metrics service might not be available
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error retrieving system status: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Shows available commands
    /// </summary>
    public static void ShowHelp()
    {
        System.Console.WriteLine("🚀 SmaTrendFollower Enhanced Commands (Phases 3 & 4)");
        System.Console.WriteLine();
        System.Console.WriteLine("📋 Available Commands:");
        System.Console.WriteLine("  health      - 🏥 Show system health status");
        System.Console.WriteLine("  metrics     - 📈 Show trading metrics and statistics");
        System.Console.WriteLine("  live        - 📡 Show live market data and signals");
        System.Console.WriteLine("  system      - 🖥️  Show system status and information");
        System.Console.WriteLine("  metrics-api - 🌐 Start web dashboard API service");
        System.Console.WriteLine();
        System.Console.WriteLine("💡 Usage Examples:");
        System.Console.WriteLine("  dotnet run -- health");
        System.Console.WriteLine("  dotnet run -- metrics");
        System.Console.WriteLine("  dotnet run -- live");
        System.Console.WriteLine("  dotnet run -- system");
        System.Console.WriteLine("  dotnet run -- metrics-api");
        System.Console.WriteLine();
        System.Console.WriteLine("🌐 Web Dashboard:");
        System.Console.WriteLine("  After starting metrics-api, visit:");
        System.Console.WriteLine("  http://localhost:8080/dashboard");
        System.Console.WriteLine();
        System.Console.WriteLine("🎯 Phase 3: Live Intelligence & Streaming - ✅ IMPLEMENTED");
        System.Console.WriteLine("🔍 Phase 4: Observability & Deployment - ✅ IMPLEMENTED");
    }

    /// <summary>
    /// Runs a command with proper service setup
    /// </summary>
    public static async Task RunCommandAsync(string command, Func<IServiceProvider, Task> handler)
    {
        try
        {
            var host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Configure all services needed for the command
                    ConfigureCommandServices(services);
                })
                .Build();

            using var scope = host.Services.CreateScope();
            await handler(scope.ServiceProvider);
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error executing {command} command: {ex.Message}");
        }
    }

    /// <summary>
    /// Configures services for command execution using centralized configuration
    /// </summary>
    private static void ConfigureCommandServices(IServiceCollection services)
    {
        // Load environment variables
        DotNetEnv.Env.Load();

        // Use centralized service configuration to avoid duplicates
        services.AddFullTradingSystem();
        services.AddTradingServiceImplementation(useEnhanced: true);

        // Add command-specific services that aren't in the main configuration
        services.AddScoped<IRealTimeMarketMonitor, RealTimeMarketMonitor>();
        services.AddScoped<ILiveSignalIntelligence, LiveSignalIntelligence>();
        services.AddSingleton<ITradingMetricsService, TradingMetricsService>();
        services.AddSingleton<ISystemHealthService, SystemHealthService>();
    }
}
